package com.btpnsyariah.agendaku.cashmanager.other.repository;

import com.btpnsyariah.agendaku.cashmanager.other.model.FlyerEntity;
import com.btpnsyariah.agendaku.cashmanager.other.model.NationalHoliday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FlyerRepository extends JpaRepository<FlyerEntity, Long> {
    @Query(nativeQuery = true, value = "SELECT * FROM FlyerConfig WHERE CONVERT(DATE, StartDate) BETWEEN CONVERT(DATE, :startDate) AND CONVERT(DATE, :endDate)")
    List<FlyerEntity> findAllFlyerStartingIn(Date startDate, Date endDate);
}
