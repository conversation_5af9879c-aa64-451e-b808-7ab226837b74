package com.btpnsyariah.agendaku.cashmanager.other.controller;

import com.btpnsyariah.agendaku.cashmanager.other.model.FlyerDTO;
import com.btpnsyariah.agendaku.cashmanager.other.service.FlyerService;
import com.btpnsyariah.agendaku.cashmanager.util.Constants;
import com.btpnsyariah.agendaku.cashmanager.util.MinioService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import org.apache.commons.io.IOUtils;

@RestController
@RequestMapping("/flyer")
public class FlyerController {

    @Autowired
    FlyerService flyerService;
    @Autowired
    MinioService minioService;

    @Autowired
    ObjectMapper objectMapper;

    @Operation(summary = "Submit Flyer")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @PostMapping(value = "/save", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity persistFlyer(@RequestHeader("Access-Token") String token,
                                       @RequestParam("file") MultipartFile file,
                                       @RequestParam("data") String jsonData) throws IOException {
        try {
            FlyerDTO flyerData = objectMapper.readValue(jsonData, FlyerDTO.class);
            flyerService.persistFlyer(file, flyerData, token);
            return new ResponseEntity<>("Flyer persisted!", HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @GetMapping("/file")
    public ResponseEntity getFlyerFile(@RequestParam(required = false, value = "fileName") String fileName,
                                       @RequestHeader("Access-Token") String token){
        try {
            InputStream inputStream = minioService.getFile(fileName, token);
            byte[] byteArray = IOUtils.toByteArray(inputStream);
            return new ResponseEntity<>(byteArray, HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @GetMapping("/download")
    public ResponseEntity downloadFlyer(@RequestParam(required = false, value = "fileName") String fileName,
                                       @RequestHeader("Access-Token") String token){
        try {
            InputStream inputStream = minioService.getFile(fileName, token);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"file.pdf\"")
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(new InputStreamResource(inputStream));
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
