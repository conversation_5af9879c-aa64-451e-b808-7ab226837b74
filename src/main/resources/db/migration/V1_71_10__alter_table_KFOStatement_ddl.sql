
IF COL_LENGTH('dbo.KFOStatement','ApprovedNik') IS NULL
ALTER TABLE KFOStatement ADD ApprovedNik NVARCHAR(50);

IF COL_LENGTH('dbo.KFOStatement','ApprovedName') IS NULL
ALTER TABLE KFOStatement ADD ApprovedName NVARCHAR(200);

IF COL_LENGTH('dbo.KFOStatement','DeclineNik') IS NULL
ALTER TABLE KFOStatement ADD DeclineNik NVARCHAR(50);

IF COL_LENGTH('dbo.KFOStatement','DeclineName') IS NULL
ALTER TABLE KFOStatement ADD DeclineName NVARCHAR(200);

IF COL_LENGTH('dbo.KFOStatement','ApprovedDate') IS NULL
ALTER TABLE KFOStatement ADD ApprovedDate DATETIME;

IF COL_LENGTH('dbo.KFOStatement','DeclineDate') IS NULL
ALTER TABLE KFOStatement ADD DeclineDate DATETIME;

IF COL_LENGTH('dbo.KFOStatement','ReverseRequestedDate') IS NULL
ALTER TABLE KFOStatement ADD ReverseRequestedDate DATETIME;

IF COL_LENGTH('dbo.KFOStatement','ReverseApprovedDate') IS NULL
ALTER TABLE KFOStatement ADD ReverseApprovedDate DATETIME;

IF COL_LENGTH('dbo.KFOStatement','RequestStatus') IS NULL
ALTER TABLE KFOStatement ADD RequestStatus NVARCHAR(100);

IF COL_LENGTH('dbo.KFOStatement','ReverseStatus') IS NOT NULL
ALTER TABLE KFOStatement DROP COLUMN ReverseStatus;
