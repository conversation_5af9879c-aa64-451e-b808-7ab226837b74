IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashCountTransaction' AND xtype='U')
CREATE TABLE CashCountTransaction(
 	id BIGINT PRIMARY KEY NOT NULL IDENTITY,
 	MMSCode VARCHAR(50),
 	CashCountType VARCHAR(50),
 	CreatedDate DATETIME,
 	CreatedBy VARCHAR(50),
 	UpdatedDate DATETIME,
 	UpdatedBy VARCHAR(50),
 	HundredThousands INT DEFAULT 0,
 	HundredThousandsBundle INT DEFAULT 0,
 	SeventyFiveThousands INT DEFAULT 0,
 	SeventyFiveThousandsBundle INT DEFAULT 0,
 	FiftyThousands INT DEFAULT 0,
 	FiftyThousandsBundle INT DEFAULT 0,
 	TwentyThousands INT DEFAULT 0,
    TwentyThousandsBundle INT DEFAULT 0,
    TenThousands INT DEFAULT 0,
    TenThousandsBundle INT DEFAULT 0,
    FiveThousands INT DEFAULT 0,
    FiveThousandsBundle INT DEFAULT 0,
    TwoThousands INT DEFAULT 0,
    TwoT<PERSON>sandsBundle INT DEFAULT 0,
    OneThousands INT DEFAULT 0,
    OneThousandsBundle INT DEFAULT 0,
    FiveHundreds INT DEFAULT 0,
    FiveHundredsBundle INT DEFAULT 0,
    TwoHundreds INT DEFAULT 0,
    TwoHundredsBundle INT DEFAULT 0,
    OneHundreds INT DEFAULT 0,
    OneHundredsBundle INT DEFAULT 0,
    SmallMoney INT DEFAULT 0,
    TotalAmount DECIMAL(22,4) DEFAULT 0.0000,
    ExecutedBy VARCHAR(40),
    ApprovedBy VARCHAR(40)
 )