IF EXISTS (SELECT * FROM sysobjects WHERE name='CoDailyTransactionCenterDetail' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.CoDailyTransactionCenterDetail','UseT24') IS NOT NULL
    ALTER TABLE CoDailyTransactionCenterDetail DROP COLUMN Type;
    IF COL_LENGTH('dbo.CoDailyTransactionCenterDetail','CenterCode') IS NULL
    ALTER TABLE CoDailyTransactionCenterDetail ADD CenterCode VARCHAR(10);
    IF COL_LENGTH('dbo.CoDailyTransactionCenterDetail','CenterName') IS NULL
    ALTER TABLE CoDailyTransactionCenterDetail ADD CenterName VARCHAR(100);
    IF COL_LENGTH('dbo.CoDailyTransactionCenterDetail','Amount') IS NULL
    ALTER TABLE CoDailyTransactionCenterDetail ADD Amount DECIMAL(22,4) DEFAULT 0.0000;
    IF COL_LENGTH('dbo.CoDailyTransactionCenterDetail','TransactionDate') IS NULL
    ALTER TABLE CoDailyTransactionCenterDetail ADD TransactionDate VARCHAR(10);
END