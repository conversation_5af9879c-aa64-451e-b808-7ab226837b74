IF COL_LENGTH('dbo.KFOStatement','KFOCode') IS NULL
ALTER TABLE KFOStatement ADD KFOCode NVARCHAR(50);

IF COL_LENGTH('dbo.KFOStatement','City') IS NULL
ALTER TABLE KFOStatement ADD City NVARCHAR(50);

IF COL_LENGTH('dbo.KFOStatement','FundSource') IS NULL
ALTER TABLE KFOStatement ADD FundSource NVARCHAR(100);

IF COL_LENGTH('dbo.KFOStatement','ReverseStatus') IS NULL
ALTER TABLE KFOStatement ADD ReverseStatus NVARCHAR(100);

IF COL_LENGTH('dbo.KFOStatement','ReverseRequestedNik') IS NULL
ALTER TABLE KFOStatement ADD ReverseRequestedNik NVARCHAR(50);

IF COL_LENGTH('dbo.KFOStatement','ReverseRequestedName') IS NULL
ALTER TABLE KFOStatement ADD ReverseRequestedName NVARCHAR(200);

IF COL_LENGTH('dbo.KFOStatement','ReverseApprovedNik') IS NULL
ALTER TABLE KFOStatement ADD ReverseApprovedNik NVARCHAR(50);

IF COL_LENGTH('dbo.KFOStatement','ReverseApprovedName') IS NULL
ALTER TABLE KFOStatement ADD ReverseApprovedName NVARCHAR(200);
