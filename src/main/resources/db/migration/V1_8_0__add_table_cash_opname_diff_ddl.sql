IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashOpnameTransactionDifference' AND xtype='U')
  CREATE TABLE CashOpnameTransactionDifference (
            id bigint primary key not null identity,
            CashOpnameId bigint not null,
            DifferenceStatus VARCHAR(20),
            HundredThousands INT DEFAULT (0),
            FiftyThousands INT DEFAULT (0),
            TwentyThousands INT DEFAULT (0),
            TenThousands INT DEFAULT (0),
            FiveThousands INT DEFAULT (0),
            TwoThousands INT DEFAULT (0),
            OneThousands INT DEFAULT (0),
            FiveHundreds INT DEFAULT (0),
            TwoHundreds INT DEFAULT (0),
            OneHundreds INT DEFAULT (0),
            SmallMoney INT DEFAULT (0),
            TotalAmount DECIMAL(22,4) DEFAULT 0.0000,
            Notes VARCHAR(500),
            CreatedDate DATETIME,
            CreatedBy VARCHAR(50),
            UpdatedDate DATETIME,
            UpdatedBy VARCHAR(50)
  )